<ui:UXML xmlns:ui="UnityEngine.UIElements" xmlns:uie="UnityEditor.UIElements" editor-extension-mode="False">
    <ui:Template name="CharacterDeckTile" src="project://database/Assets/Scripts/UI/Source/CharacterDeckTile.uxml?fileID=9197481963319205126&amp;guid=072a9f08807331d4395e4d01b1f3a0c9&amp;type=3#CharacterDeckTile" />
    <ui:Template name="DeckSelectedCharacterContainer" src="project://database/Assets/Scripts/UI/Source/DeckSelectedCharacterContainer.uxml?fileID=9197481963319205126&amp;guid=75f1854098eca6a44bef5117c6fe2f21&amp;type=3#DeckSelectedCharacterContainer" />
    <Style src="project://database/Assets/Scripts/UI/Styles/CharacterDeck.uss?fileID=7433441132597879392&amp;guid=bb96079b731b72a4480be164e391518e&amp;type=3#CharacterDeck" />
    <CharacterDeckUI style="flex-grow: 1; align-content: stretch; align-items: flex-start; justify-content: center; align-self: flex-start; padding-top: 37px; padding-right: 13px; padding-bottom: 20px; padding-left: 20px; display: flex;">
        <ui:VisualElement name="TopStack" class="deck-scroll-container top">
            <ui:Instance template="CharacterDeckTile"/>
            <ui:Instance template="CharacterDeckTile"/>
            <ui:Instance template="CharacterDeckTile"/>
            <ui:Instance template="CharacterDeckTile"/>
            <ui:Instance template="CharacterDeckTile"/>
            <ui:Instance template="CharacterDeckTile"/>
            <ui:Instance template="CharacterDeckTile"/>
        </ui:VisualElement>
        <ui:Instance template="DeckSelectedCharacterContainer" name="DeckSelectedCharacterContainer" />
        <ui:VisualElement name="BottomStack" class="deck-scroll-container bottom">
            <ui:Instance template="CharacterDeckTile"/>
            <ui:Instance template="CharacterDeckTile"/>
            <ui:Instance template="CharacterDeckTile"/>
            <ui:Instance template="CharacterDeckTile"/>
            <ui:Instance template="CharacterDeckTile"/>
            <ui:Instance template="CharacterDeckTile"/>
        </ui:VisualElement>
    </CharacterDeckUI>
</ui:UXML>
