
using GenericEventSystem;
using UnityEngine;
using UnityEngine.UIElements;

public class GameplayUI : MonoBehaviour {
    [SerializeField] VisualTreeAsset characterTileAsset;

    private UIDocument uiDocument => GetComponent<UIDocument>();
    private VisualElement _root;
    private VisualElement root => _root ??= uiDocument.rootVisualElement;
    private CharacterDeckUI _characterDeck;

    private CharacterDeckUI CharacterDeck =>
        _characterDeck ??= root.Q<CharacterDeckUI>("CharacterDeckUI");

    void Start() {
        uiDocument.enabled = true;
        root.style.display = DisplayStyle.None;
        GenericEventSystem.EventCoordinator.StartListening(EventName.World.InitialDialogueEnded(), OnStartMission);
    }

    private void OnStartMission(GameMessage msg) {
        root.style.display = DisplayStyle.Flex;

        if (CharacterDeck != null && characterTileAsset != null) {
            Debug.Log("Initializing CharacterDeckUI");
            CharacterDeck.Init(characterTileAsset);
        }
    }

    private void OnDestroy() {
        if (CharacterDeck != null) {
            CharacterDeck.Cleanup();
        }
    }
}
